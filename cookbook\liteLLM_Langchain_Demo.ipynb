{"cells": [{"cell_type": "markdown", "metadata": {"id": "5hwntUxTMxEk"}, "source": ["# Langchain liteLLM Demo Notebook\n", "## Use `ChatLiteLLM()` to instantly support 50+ LLM models\n", "Langchain Docs: https://python.langchain.com/docs/integrations/chat/litellm\n", "\n", "Call all LLM models using the same I/O interface\n", "\n", "Example usage\n", "```python\n", "ChatLiteLLM(model=\"gpt-3.5-turbo\")\n", "ChatLiteLLM(model=\"claude-2\", temperature=0.3)\n", "ChatLiteLLM(model=\"command-nightly\")\n", "ChatLiteLLM(model=\"replicate/llama-2-70b-chat:2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1\")\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aPNAUsCvB6Sv"}, "outputs": [], "source": ["!pip install litellm langchain"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "MOhRaVnhB-0J"}, "outputs": [], "source": ["import os\n", "from langchain.chat_models import ChatLiteLLM\n", "from langchain.schema import HumanMessage"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TahkCtlmCD65", "outputId": "5ddda40f-f252-4830-a8d6-bd3fa68ae487"}, "outputs": [{"data": {"text/plain": ["AIMessage(content='I am an AI model known as GPT-3, developed by OpenAI.', additional_kwargs={}, example=False)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['OPENAI_API_KEY'] = \"\"\n", "chat = ChatLiteLLM(model=\"gpt-3.5-turbo\")\n", "messages = [\n", "    HumanMessage(\n", "        content=\"what model are you\"\n", "    )\n", "]\n", "chat(messages)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uXNDyU4jChcs", "outputId": "bd74b4c6-f9fb-42dc-fdc3-9240d50503ba"}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\" I'm <PERSON>, an AI assistant created by <PERSON><PERSON><PERSON>.\", additional_kwargs={}, example=False)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['ANTHROPIC_API_KEY'] = \"\"\n", "chat = ChatLiteLLM(model=\"claude-2\", temperature=0.3)\n", "messages = [\n", "    HumanMessage(\n", "        content=\"what model are you\"\n", "    )\n", "]\n", "chat(messages)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "czbDJRKcC7BV", "outputId": "892e147d-831e-4884-dc71-040f92c3fb8e"}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\" I'm an AI based based on LLaMA models (LLaMA: Open and Efficient Foundation Language Models, Touvron et al. 2023), my knowledge was built from a massive corpus of text, including books, articles, and websites, and I was trained using a variety of machine learning algorithms. My model architecture is based on the transformer architecture, which is particularly well-suited for natural language processing tasks. My team of developers and I are constantly working to improve and fine-tune my performance, and I am always happy to help with any questions you may have!\", additional_kwargs={}, example=False)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['REPLICATE_API_TOKEN'] = \"\"\n", "chat = ChatLiteLLM(model=\"replicate/llama-2-70b-chat:2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1\")\n", "messages = [\n", "    HumanMessage(\n", "        content=\"what model are you?\"\n", "    )\n", "]\n", "chat(messages)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tZxpq5PDDY9Y", "outputId": "7e86f4ed-ac7a-45e1-87d0-217da6cad666"}, "outputs": [{"data": {"text/plain": ["AIMessage(content=' I am an AI-based large language model, or Chatbot, built by the company Cohere. I am designed to have polite, helpful, inclusive conversations with users. I am always learning and improving, and I am constantly being updated with new information and improvements.\\n\\nI am currently in the development phase, and I am not yet available to the general public. However, I am currently being used by a select group of users for testing and feedback.\\n\\nI am a large language model, which means that I am trained on a massive amount of data and can understand and respond to a wide range of requests and questions. I am also designed to be flexible and adaptable, so I can be customized to suit the needs of different users and use cases.\\n\\nI am currently being used to develop a range of applications, including customer service chatbots, content generation tools, and language translation services. I am also being used to train other language models and to develop new ways of using large language models.\\n\\nI am constantly being updated with new information and improvements, so I am always learning and improving. I am also being used to develop new ways of using large language models, so I am always evolving and adapting to new use cases and requirements.', additional_kwargs={}, example=False)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['COHERE_API_KEY'] = \"\"\n", "chat = ChatLiteLLM(model=\"command-nightly\")\n", "messages = [\n", "    HumanMessage(\n", "        content=\"what model are you?\"\n", "    )\n", "]\n", "chat(messages)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}