apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "litellm.fullname" . }}-test-connection"
  labels:
    {{- include "litellm.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['sh', '-c']
      args:
        - |
          # Wait for a bit to allow the service to be ready
          sleep 10
          # Try multiple times with a delay between attempts
          for i in $(seq 1 30); do
            wget -T 5 "{{ include "litellm.fullname" . }}:{{ .Values.service.port }}/health/readiness" && exit 0
            echo "Attempt $i failed, waiting..."
            sleep 2
          done
          exit 1
  restartPolicy: Never