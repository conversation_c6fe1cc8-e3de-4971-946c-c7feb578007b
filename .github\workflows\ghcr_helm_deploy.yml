# this workflow is triggered by an API call when there is a new PyPI release of LiteLLM
name: Build, Publish LiteLLM Helm Chart. New Release
on:
  workflow_dispatch:
    inputs:
      chartVersion:
        description: "Update the helm chart's version to this"

# Defines two custom environment variables for the workflow. Used for the Container registry domain, and a name for the Docker image that this workflow builds.
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  REPO_OWNER: ${{github.repository_owner}}

# There is a single job in this workflow. It's configured to run on the latest available version of Ubuntu.
jobs:        
  build-and-push-helm-chart:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: lowercase github.repository_owner
        run: |
          echo "REPO_OWNER=`echo ${{github.repository_owner}} | tr '[:upper:]' '[:lower:]'`" >>${GITHUB_ENV}

      - name: Get LiteLLM Latest Tag
        id: current_app_tag
        uses: WyriHaximus/github-action-get-previous-tag@v1.3.0

      - name: Get last published chart version
        id: current_version
        shell: bash
        run: helm show chart oci://${{ env.REGISTRY }}/${{ env.REPO_OWNER }}/litellm-helm | grep '^version:' | awk 'BEGIN{FS=":"}{print "current-version="$2}' | tr -d " " | tee -a $GITHUB_OUTPUT
        env:
          HELM_EXPERIMENTAL_OCI: '1'

      # Automatically update the helm chart version one "patch" level
      - name: Bump release version
        id: bump_version
        uses: christian-draeger/increment-semantic-version@1.1.0
        with:
          current-version: ${{ steps.current_version.outputs.current-version || '0.1.0' }}
          version-fragment: 'bug'

      - name: Lint helm chart
        run: helm lint deploy/charts/litellm-helm

      - uses: ./.github/actions/helm-oci-chart-releaser
        with:
          name: litellm-helm
          repository: ${{ env.REPO_OWNER }}
          tag: ${{ github.event.inputs.chartVersion || steps.bump_version.outputs.next-version || '0.1.0' }}
          app_version: ${{ steps.current_app_tag.outputs.tag || 'latest' }}
          path: deploy/charts/litellm-helm
          registry: ${{ env.REGISTRY }}
          registry_username: ${{ github.actor }}
          registry_password: ${{ secrets.GITHUB_TOKEN }}
          update_dependencies: true
  
