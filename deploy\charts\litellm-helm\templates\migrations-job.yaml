{{- if .Values.migrationJob.enabled }}
# This job runs the Prisma migrations for the LiteLLM DB.
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "litellm.fullname" . }}-migrations
  labels:
    {{- include "litellm.labels" . | nindent 4 }}
  annotations:
    {{- if .Values.migrationJob.hooks.argocd.enabled }}
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
    {{- end }}
    {{- if .Values.migrationJob.hooks.helm.enabled }}
    helm.sh/hook: "pre-install,pre-upgrade"
    helm.sh/hook-delete-policy: "before-hook-creation"
    helm.sh/hook-weight: {{ .Values.migrationJob.hooks.helm.weight | default "1" | quote }}
    {{- end }}
    checksum/config: {{ toYaml .Values | sha256sum }}
spec:
  template:
    metadata:
      labels:
        {{- include "litellm.labels" . | nindent 8 }}
      annotations:
        {{- with .Values.migrationJob.annotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "litellm.serviceAccountName" . }}
      containers:
        - name: prisma-migrations
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default (printf "main-%s" .Chart.AppVersion) }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          command: ["python", "litellm/proxy/prisma_migration.py"]
          workingDir: "/app"
          env:
            {{- if .Values.db.useExisting }}
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.usernameKey }}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.passwordKey }}
            - name: DATABASE_HOST
              {{- if .Values.db.secret.endpointKey }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.endpointKey }}
              {{- else }}
              value: {{ .Values.db.endpoint }}
              {{- end }}
            - name: DATABASE_NAME
              value: {{ .Values.db.database }}
            - name: DATABASE_URL
              value: {{ .Values.db.url | quote }}
            {{- else if .Values.db.deployStandalone }}
            - name: DATABASE_URL
              value: postgresql://{{ .Values.postgresql.auth.username }}:{{ .Values.postgresql.auth.password }}@{{ .Release.Name }}-postgresql/{{ .Values.postgresql.auth.database }}
            {{- end }}
            {{- if .Values.envVars }}
            {{- range $key, $val := .Values.envVars }}
            - name: {{ $key }}
              value: {{ $val | quote }}
            {{- end }}
            {{- end }}
            {{- with .Values.extraEnvVars }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
            - name: DISABLE_SCHEMA_UPDATE
              value: "false" # always run the migration from the Helm PreSync hook, override the value set
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.migrationJob.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.migrationJob.extraContainers }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: OnFailure
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  ttlSecondsAfterFinished: {{ .Values.migrationJob.ttlSecondsAfterFinished }}
  backoffLimit: {{ .Values.migrationJob.backoffLimit }}
{{- end }}
