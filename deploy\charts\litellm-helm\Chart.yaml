apiVersion: v2

# We can't call ourselves just "litellm" because then we couldn't publish to the
#  same OCI repository as the "litellm" OCI image
name: litellm-helm
description: Call all LLM APIs using the OpenAI format

# A chart can be either an 'application' or a 'library' chart.
#
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.4.6

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: v1.50.2

dependencies:
  - name: "postgresql"
    version: ">=13.3.0"
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: db.deployStandalone
  - name: redis
    version: ">=18.0.0"
    repository: oci://registry-1.docker.io/bitnamicharts 
    condition: redis.enabled
