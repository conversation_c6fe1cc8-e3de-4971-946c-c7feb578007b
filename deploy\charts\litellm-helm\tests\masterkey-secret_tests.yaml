suite: test masterkey secret
templates:
  - secret-masterkey.yaml
tests:
  - it: should create a secret if masterkeySecretName is not set. should start with sk-xxxx (base64 encoded as c2st*)
    template: secret-masterkey.yaml
    set:
      masterkeySecretName: ""
    asserts:
      - isKind:
          of: Secret
      - matchRegex:
          path: data.masterkey
          pattern: ^c2st
          # Note: The masterkey is generated as "sk-<18-random-chars>" in plain text,
          # but stored as base64 encoded in Kubernetes secret (requirement).
          # "sk-" base64 encodes to "c2st", so we check for "^c2st" pattern.
  - it: should not create a secret if masterkeySecretName is set
    template: secret-masterkey.yaml
    set:
      masterkeySecretName: my-secret
    asserts:
      - hasDocuments:
          count: 0
