name: Updates model_prices_and_context_window.j<PERSON> and <PERSON><PERSON> Pull Request

on:
  schedule:
    - cron: "0 0 * * 0"  # Run every Sundays at midnight
    #- cron: "0 0 * * *" # Run daily at midnight

jobs:
  auto_update_price_and_context_window:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install Dependencies
        run: |
          pip install aiohttp
      - name: Update JSON Data
        run: |
          python ".github/workflows/auto_update_price_and_context_window_file.py"
      - name: Create Pull Request
        run: |
          git add model_prices_and_context_window.json 
          git commit -m "Update model_prices_and_context_window.json file: $(date +'%Y-%m-%d')" 
          gh pr create --title "Update model_prices_and_context_window.json file" \
            --body "Automated update for model_prices_and_context_window.json" \
            --head auto-update-price-and-context-window-$(date +'%Y-%m-%d') \
            --base main
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}