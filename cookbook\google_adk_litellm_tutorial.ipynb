{"cells": [{"cell_type": "markdown", "id": "7aa8875d", "metadata": {}, "source": ["# Google ADK with LiteLLM\n", "\n", "Use Google ADK with LiteLLM Python SDK, LiteLLM Proxy.\n", "\n", "This tutorial shows you how to create intelligent agents using Agent Development Kit (ADK) with support for multiple Large Language Model (LLM) providers through LiteLLM."]}, {"cell_type": "markdown", "id": "a4d249c3", "metadata": {}, "source": ["## Overview\n", "\n", "ADK (Agent Development Kit) allows you to build intelligent agents powered by LLMs. By integrating with LiteLLM, you can:\n", "\n", "- Use multiple LLM providers (OpenAI, Anthropic, Google, etc.)\n", "- Switch easily between models from different providers\n", "- Connect to a LiteLLM proxy for centralized model management"]}, {"cell_type": "markdown", "id": "a0bbb56b", "metadata": {}, "source": ["## Prerequisites\n", "\n", "- Python environment setup\n", "- API keys for model providers (OpenAI, Anthropic, Google AI Studio)\n", "- Basic understanding of LLMs and agent concepts"]}, {"cell_type": "markdown", "id": "7fee50a8", "metadata": {}, "source": ["## Installation"]}, {"cell_type": "code", "execution_count": null, "id": "44106a23", "metadata": {}, "outputs": [], "source": ["# Install dependencies\n", "!pip install google-adk litellm"]}, {"cell_type": "markdown", "id": "2171740a", "metadata": {}, "source": ["## 1. Setting Up Environment"]}, {"cell_type": "code", "execution_count": null, "id": "6695807e", "metadata": {}, "outputs": [], "source": ["# Setup environment and API keys\n", "import os\n", "import asyncio\n", "from google.adk.agents import Agent\n", "from google.adk.models.lite_llm import LiteLlm  # For multi-model support\n", "from google.adk.sessions import InMemorySessionService\n", "from google.adk.runners import Runner\n", "from google.genai import types\n", "import litellm  # Import for proxy configuration\n", "\n", "# Set your API keys\n", "os.environ['GOOGLE_API_KEY'] = 'your-google-api-key'  # For Gemini models\n", "os.environ['OPENAI_API_KEY'] = 'your-openai-api-key'  # For OpenAI models\n", "os.environ['ANTHROPIC_API_KEY'] = 'your-anthropic-api-key'  # For Claude models\n", "\n", "# Define model constants for cleaner code\n", "MODEL_GEMINI_PRO = 'gemini-1.5-pro'\n", "MODEL_GPT_4O = 'openai/gpt-4o'\n", "MODEL_CLAUDE_SONNET = 'anthropic/claude-3-sonnet-20240229'"]}, {"cell_type": "markdown", "id": "d2b1ed59", "metadata": {}, "source": ["## 2. Define a Simple Tool"]}, {"cell_type": "code", "execution_count": null, "id": "04b3ef5b", "metadata": {}, "outputs": [], "source": ["# Weather tool implementation\n", "def get_weather(city: str) -> dict:\n", "    \"\"\"Retrieves the current weather report for a specified city.\"\"\"\n", "    print(f'Tool: get_weather called for city: {city}')\n", "\n", "    # Mock weather data\n", "    mock_weather_db = {\n", "        'newyork': {\n", "            'status': 'success',\n", "            'report': 'The weather in New York is sunny with a temperature of 25°C.'\n", "        },\n", "        'london': {\n", "            'status': 'success',\n", "            'report': \"It's cloudy in London with a temperature of 15°C.\"\n", "        },\n", "        'tokyo': {\n", "            'status': 'success',\n", "            'report': 'Tokyo is experiencing light rain and a temperature of 18°C.'\n", "        },\n", "    }\n", "\n", "    city_normalized = city.lower().replace(' ', '')\n", "\n", "    if city_normalized in mock_weather_db:\n", "        return mock_weather_db[city_normalized]\n", "    else:\n", "        return {\n", "            'status': 'error',\n", "            'error_message': f\"Sorry, I don't have weather information for '{city}'.\"\n", "        }"]}, {"cell_type": "markdown", "id": "727b15c9", "metadata": {}, "source": ["## 3. Helper Function for Agent Interaction"]}, {"cell_type": "code", "execution_count": null, "id": "f77449bf", "metadata": {}, "outputs": [], "source": ["# Agent interaction helper function\n", "async def call_agent_async(query: str, runner, user_id, session_id):\n", "    \"\"\"Sends a query to the agent and prints the final response.\"\"\"\n", "    print(f'\\n>>> User Query: {query}')\n", "\n", "    content = types.Content(role='user', parts=[types.Part(text=query)])\n", "    final_response_text = 'Agent did not produce a final response.'\n", "\n", "    async for event in runner.run_async(\n", "        user_id=user_id,\n", "        session_id=session_id,\n", "        new_message=content\n", "    ):\n", "        if event.is_final_response():\n", "            if event.content and event.content.parts:\n", "                final_response_text = event.content.parts[0].text\n", "            break\n", "    print(f'<<< Agent Response: {final_response_text}')"]}, {"cell_type": "markdown", "id": "0ac87987", "metadata": {}, "source": ["## 4. Using Different Model Providers with ADK\n", "\n", "### 4.1 Using OpenAI Models"]}, {"cell_type": "code", "execution_count": null, "id": "e167d557", "metadata": {}, "outputs": [], "source": ["# OpenAI model implementation\n", "weather_agent_gpt = Agent(\n", "    name='weather_agent_gpt',\n", "    model=LiteLlm(model=MODEL_GPT_4O),\n", "    description='Provides weather information using OpenAI\\'s GPT.',\n", "    instruction=(\n", "        'You are a helpful weather assistant powered by GPT-4o. '\n", "        \"Use the 'get_weather' tool for city weather requests. \"\n", "        'Present information clearly.'\n", "    ),\n", "    tools=[get_weather],\n", ")\n", "\n", "session_service_gpt = InMemorySessionService()\n", "session_gpt = session_service_gpt.create_session(\n", "    app_name='weather_app', user_id='user_1', session_id='session_gpt'\n", ")\n", "\n", "runner_gpt = Runner(\n", "    agent=weather_agent_gpt,\n", "    app_name='weather_app',\n", "    session_service=session_service_gpt,\n", ")\n", "\n", "async def test_gpt_agent():\n", "    print('\\n--- Testing GPT Agent ---')\n", "    await call_agent_async(\n", "        \"What's the weather in London?\",\n", "        runner=runner_gpt,\n", "        user_id='user_1',\n", "        session_id='session_gpt',\n", "    )\n", "\n", "# To execute in a notebook cell:\n", "# await test_gpt_agent()"]}, {"cell_type": "markdown", "id": "f9cb0613", "metadata": {}, "source": ["### 4.2 Using Anthropic Models"]}, {"cell_type": "code", "execution_count": null, "id": "1c653665", "metadata": {}, "outputs": [], "source": ["# Anthropic model implementation\n", "weather_agent_claude = Agent(\n", "    name='weather_agent_claude',\n", "    model=LiteLlm(model=MODEL_CLAUDE_SONNET),\n", "    description='Provides weather information using Anthropic\\'s <PERSON>.',\n", "    instruction=(\n", "        'You are a helpful weather assistant powered by <PERSON>. '\n", "        \"Use the 'get_weather' tool for city weather requests. \"\n", "        'Present information clearly.'\n", "    ),\n", "    tools=[get_weather],\n", ")\n", "\n", "session_service_claude = InMemorySessionService()\n", "session_claude = session_service_claude.create_session(\n", "    app_name='weather_app', user_id='user_1', session_id='session_claude'\n", ")\n", "\n", "runner_claude = Runner(\n", "    agent=weather_agent_claude,\n", "    app_name='weather_app',\n", "    session_service=session_service_claude,\n", ")\n", "\n", "async def test_claude_agent():\n", "    print('\\n--- Testing Claude Agent ---')\n", "    await call_agent_async(\n", "        \"What's the weather in Tokyo?\",\n", "        runner=runner_claude,\n", "        user_id='user_1',\n", "        session_id='session_claude',\n", "    )\n", "\n", "# To execute in a notebook cell:\n", "# await test_claude_agent()"]}, {"cell_type": "markdown", "id": "bf9d863b", "metadata": {}, "source": ["### 4.3 Using Google's Gemini Models"]}, {"cell_type": "code", "execution_count": null, "id": "83f49d0a", "metadata": {}, "outputs": [], "source": ["# Gemini model implementation\n", "weather_agent_gemini = Agent(\n", "    name='weather_agent_gemini',\n", "    model=MODEL_GEMINI_PRO,\n", "    description='Provides weather information using Google\\'s Gemini.',\n", "    instruction=(\n", "        'You are a helpful weather assistant powered by Gemini Pro. '\n", "        \"Use the 'get_weather' tool for city weather requests. \"\n", "        'Present information clearly.'\n", "    ),\n", "    tools=[get_weather],\n", ")\n", "\n", "session_service_gemini = InMemorySessionService()\n", "session_gemini = session_service_gemini.create_session(\n", "    app_name='weather_app', user_id='user_1', session_id='session_gemini'\n", ")\n", "\n", "runner_gemini = Runner(\n", "    agent=weather_agent_gemini,\n", "    app_name='weather_app',\n", "    session_service=session_service_gemini,\n", ")\n", "\n", "async def test_gemini_agent():\n", "    print('\\n--- Testing Gemini Agent ---')\n", "    await call_agent_async(\n", "        \"What's the weather in New York?\",\n", "        runner=runner_gemini,\n", "        user_id='user_1',\n", "        session_id='session_gemini',\n", "    )\n", "\n", "# To execute in a notebook cell:\n", "# await test_gemini_agent()"]}, {"cell_type": "markdown", "id": "93bc5fd0", "metadata": {}, "source": ["## 5. Using LiteLLM Proxy with ADK"]}, {"cell_type": "markdown", "id": "b4275151", "metadata": {}, "source": ["| Variable | Description |\n", "|----------|-------------|\n", "| `LITELLM_PROXY_API_KEY` | The API key for the LiteLLM proxy |\n", "| `LITELLM_PROXY_API_BASE` | The base URL for the LiteLLM proxy |\n", "| `USE_LITELLM_PROXY` or `litellm.use_litellm_proxy` | When set to True, your request will be sent to LiteLLM proxy. |"]}, {"cell_type": "code", "execution_count": null, "id": "256530a6", "metadata": {}, "outputs": [], "source": ["# LiteLLM proxy integration\n", "os.environ['LITELLM_PROXY_API_KEY'] = 'your-litellm-proxy-api-key'\n", "os.environ['LITELLM_PROXY_API_BASE'] = 'your-litellm-proxy-url'  # e.g., 'http://localhost:4000'\n", "litellm.use_litellm_proxy = True\n", "\n", "weather_agent_proxy_env = Agent(\n", "    name='weather_agent_proxy_env',\n", "    model=LiteLlm(model='gpt-4o'),\n", "    description='Provides weather information using a model from LiteLLM proxy.',\n", "    instruction=(\n", "        'You are a helpful weather assistant. '\n", "        \"Use the 'get_weather' tool for city weather requests. \"\n", "        'Present information clearly.'\n", "    ),\n", "    tools=[get_weather],\n", ")\n", "\n", "session_service_proxy_env = InMemorySessionService()\n", "session_proxy_env = session_service_proxy_env.create_session(\n", "    app_name='weather_app', user_id='user_1', session_id='session_proxy_env'\n", ")\n", "\n", "runner_proxy_env = Runner(\n", "    agent=weather_agent_proxy_env,\n", "    app_name='weather_app',\n", "    session_service=session_service_proxy_env,\n", ")\n", "\n", "async def test_proxy_env_agent():\n", "    print('\\n--- Testing Proxy-enabled Agent (Environment Variables) ---')\n", "    await call_agent_async(\n", "        \"What's the weather in London?\",\n", "        runner=runner_proxy_env,\n", "        user_id='user_1',\n", "        session_id='session_proxy_env',\n", "    )\n", "\n", "# To execute in a notebook cell:\n", "# await test_proxy_env_agent()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}