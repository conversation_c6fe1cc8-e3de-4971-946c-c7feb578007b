name: <PERSON><PERSON> OCI Chart Releaser
description: Push Helm charts to OCI-based (Docker) registries
author: se<PERSON><PERSON><PERSON><PERSON><PERSON>in
branding:
  color: yellow
  icon: upload-cloud
inputs:
  name:
    required: true
    description: Chart name
  repository:
    required: true
    description: Chart repository name
  tag:
    required: true
    description: Chart version
  app_version:
    required: true
    description: App version
  path:
    required: false
    description: Chart path (Default 'charts/{name}')
  registry:
    required: true
    description: OCI registry
  registry_username:
    required: true
    description: OCI registry username
  registry_password:
    required: true
    description: OCI registry password
  update_dependencies:
    required: false
    default: 'false'
    description: Update chart dependencies before packaging (Default 'false')
outputs:
  image:
    value: ${{ steps.output.outputs.image }}
    description: Chart image (Default '{registry}/{repository}/{image}:{tag}')
runs:
  using: composite
  steps:
    - name: Helm | Login
      shell: bash
      run: echo ${{ inputs.registry_password }} | helm registry login -u ${{ inputs.registry_username }} --password-stdin ${{ inputs.registry }}
      env:
        HELM_EXPERIMENTAL_OCI: '1'
    
    - name: <PERSON><PERSON> | Dependency
      if: inputs.update_dependencies == 'true'
      shell: bash
      run: helm dependency update ${{ inputs.path == null && format('{0}/{1}', 'charts', inputs.name) || inputs.path }}
      env:
        HELM_EXPERIMENTAL_OCI: '1'

    - name: Helm | Package
      shell: bash
      run: helm package ${{ inputs.path == null && format('{0}/{1}', 'charts', inputs.name) || inputs.path }} --version ${{ inputs.tag }} --app-version ${{ inputs.app_version }}
      env:
        HELM_EXPERIMENTAL_OCI: '1'

    - name: Helm | Push
      shell: bash
      run: helm push ${{ inputs.name }}-${{ inputs.tag }}.tgz oci://${{ inputs.registry }}/${{ inputs.repository }}
      env:
        HELM_EXPERIMENTAL_OCI: '1'

    - name: Helm | Logout
      shell: bash
      run: helm registry logout ${{ inputs.registry }}
      env:
        HELM_EXPERIMENTAL_OCI: '1'

    - name: Helm | Output
      id: output
      shell: bash
      run: echo "image=${{ inputs.registry }}/${{ inputs.repository }}/${{ inputs.name }}:${{ inputs.tag }}" >> $GITHUB_OUTPUT