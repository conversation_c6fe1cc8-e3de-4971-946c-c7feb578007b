name: Label ML Ops Team Issues

on:
  issues:
    types:
      - opened

jobs:
  add-mlops-label:
    runs-on: ubuntu-latest
    steps:
      - name: Check if ML Ops Team is selected
        uses: actions-ecosystem/action-add-labels@v1
        if: contains(github.event.issue.body, '### Are you a ML Ops Team?') && contains(github.event.issue.body, 'Yes')
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          labels: "mlops user request"
